# 福彩3D预测系统 - 用户使用指南

## 目录
1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [功能详解](#功能详解)
4. [操作指南](#操作指南)
5. [常见问题](#常见问题)
6. [技巧和建议](#技巧和建议)

## 系统概述

福彩3D预测系统是一个基于机器学习的彩票号码预测平台，通过分析历史数据、提取特征、训练模型来预测下一期可能的号码组合。

### 主要功能
- **数据管理**: 自动获取和管理福彩3D历史数据
- **特征工程**: 提取165+维度的数据特征
- **模型训练**: 支持多种机器学习算法
- **预测分析**: 生成预测结果和置信度评估
- **智能融合**: 多模型结果融合优化
- **实时监控**: 系统性能和错误监控

## 快速开始

### 1. 系统启动

#### 启动API服务
```bash
# 方法1: 直接启动
python src/api/production_main.py

# 方法2: 使用启动脚本
python start_api.py
```

#### 启动用户界面
```bash
# 方法1: 直接启动
python -m streamlit run src/ui/main.py

# 方法2: 使用启动脚本
python -m streamlit run src/ui/main.py
```

#### 一键启动（推荐）
```bash
python start_all.py
```

### 2. 访问系统
- **用户界面**: http://localhost:8501
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health/

### 3. 首次使用
1. 打开用户界面
2. 查看数据概览页面，确认数据已加载
3. 如需更新数据，点击"刷新数据"按钮
4. 开始使用预测功能

## 功能详解

### 📈 数据概览
**位置**: 主页面
**功能**: 显示系统数据状态和基本统计信息

#### 主要信息
- **数据总量**: 当前数据库中的记录数
- **最新期号**: 最新一期的开奖信息
- **数据范围**: 数据的时间跨度
- **更新状态**: 数据最后更新时间

#### 操作按钮
- **🔄 刷新数据**: 手动更新数据
- **📊 查看统计**: 显示详细统计信息
- **⚙️ 系统设置**: 配置系统参数

### 🔧 特征工程深度
**位置**: 侧边栏 → 特征工程深度
**功能**: 高级特征提取和分析

#### 特征类型
1. **基础特征**
   - 号码频率统计
   - 遗漏值分析
   - 奇偶比例

2. **时间序列特征**
   - 趋势分析
   - 周期性检测
   - 季节性模式

3. **统计特征**
   - 均值、方差
   - 偏度、峰度
   - 分位数统计

4. **组合特征**
   - 号码组合频率
   - 和值分布
   - 跨度分析

#### 操作步骤
1. 选择特征类型
2. 设置提取参数
3. 点击"开始特征提取"
4. 查看提取结果

### 🎯 训练监控深度
**位置**: 侧边栏 → 训练监控深度
**功能**: 模型训练和性能监控

#### 支持的模型
- **随机森林** (Random Forest)
- **XGBoost** (极端梯度提升)
- **LightGBM** (轻量级梯度提升)
- **神经网络** (Neural Network)

#### 训练流程
1. **数据准备**
   - 特征选择
   - 数据分割
   - 预处理

2. **模型配置**
   - 超参数设置
   - 交叉验证配置
   - 评估指标选择

3. **训练执行**
   - 实时进度显示
   - 性能指标监控
   - 早停机制

4. **结果评估**
   - 准确率分析
   - 特征重要性
   - 模型对比

### 🔮 预测分析
**位置**: 侧边栏 → 预测分析
**功能**: 生成预测结果和分析报告

#### 预测配置
- **预测期数**: 选择要预测的期数
- **模型选择**: 选择使用的预测模型
- **置信度阈值**: 设置结果过滤条件

#### 预测结果
1. **候选号码排行榜**
   - Top-10预测号码
   - 置信度评分
   - 概率分布

2. **详细分析**
   - 预测依据
   - 特征贡献度
   - 历史相似度

3. **风险评估**
   - 预测可靠性
   - 不确定性分析
   - 建议策略

### 🧠 智能融合优化
**位置**: 侧边栏 → 智能融合优化
**功能**: 多模型结果融合和优化

#### 融合策略
1. **加权平均**
   - 基于模型性能的权重分配
   - 动态权重调整

2. **投票机制**
   - 多数投票
   - 加权投票

3. **集成学习**
   - Stacking集成
   - Blending融合

#### 优化参数
- **短期趋势权重**: 近期数据的影响权重
- **长期模式权重**: 历史模式的影响权重
- **形态转换敏感度**: 对模式变化的敏感程度

### 📊 多维度可视化
**位置**: 各功能页面内嵌
**功能**: 数据和结果的可视化展示

#### 图表类型
- **时间序列图**: 显示数据趋势
- **分布直方图**: 显示数据分布
- **热力图**: 显示相关性矩阵
- **散点图**: 显示特征关系
- **箱线图**: 显示数据分布特征

### 🐛 Bug检测系统
**位置**: 后台运行，实时监控
**功能**: 系统错误检测和报告

#### 监控内容
- JavaScript错误
- API请求错误
- 数据处理错误
- 系统性能异常

## 操作指南

### 日常使用流程

#### 1. 数据更新
```
打开系统 → 数据概览 → 检查数据状态 → 刷新数据（如需要）
```

#### 2. 特征提取
```
特征工程深度 → 选择特征类型 → 设置参数 → 开始提取 → 查看结果
```

#### 3. 模型训练
```
训练监控深度 → 选择模型 → 配置参数 → 开始训练 → 监控进度 → 评估结果
```

#### 4. 预测分析
```
预测分析 → 配置预测参数 → 执行预测 → 查看结果 → 分析报告
```

#### 5. 结果优化
```
智能融合优化 → 选择融合策略 → 调整参数 → 执行融合 → 对比结果
```

### 高级操作

#### 批量预测
1. 在预测分析页面选择"批量预测"
2. 设置预测期数范围
3. 选择多个模型
4. 执行批量预测
5. 导出结果报告

#### 模型对比
1. 在训练监控页面训练多个模型
2. 记录各模型性能指标
3. 使用预测分析对比预测结果
4. 选择最优模型组合

#### 参数调优
1. 使用网格搜索功能
2. 设置参数搜索范围
3. 执行自动调优
4. 分析调优结果
5. 应用最优参数

## 常见问题

### Q1: 系统启动失败怎么办？
**A**: 
1. 检查Python版本（需要3.11+）
2. 确认所有依赖已安装：`pip install -r requirements.txt`
3. 检查端口是否被占用（8000, 8501）
4. 查看错误日志定位问题

### Q2: 数据更新失败怎么办？
**A**:
1. 检查网络连接
2. 确认数据源可访问
3. 检查磁盘空间
4. 查看数据更新日志

### Q3: 预测结果不准确怎么办？
**A**:
1. 检查数据质量和完整性
2. 尝试不同的特征组合
3. 调整模型参数
4. 使用多模型融合
5. 增加训练数据量

### Q4: 系统运行缓慢怎么办？
**A**:
1. 检查系统资源使用情况
2. 清理缓存文件
3. 优化数据库查询
4. 减少并发操作
5. 升级硬件配置

### Q5: 如何备份数据？
**A**:
1. 复制`lottery_data.db`文件
2. 备份`data/cache`目录
3. 导出预测结果
4. 保存模型文件

## 技巧和建议

### 使用技巧

#### 1. 数据管理
- 定期备份数据库
- 设置自动更新时间（建议晚上21:30后）
- 监控数据质量和完整性

#### 2. 特征工程
- 尝试不同的特征组合
- 关注特征重要性排序
- 定期更新特征提取策略

#### 3. 模型训练
- 使用交叉验证评估模型
- 保存表现良好的模型
- 定期重新训练模型

#### 4. 预测优化
- 结合多个模型的预测结果
- 关注预测的置信度
- 分析预测失败的案例

### 最佳实践

#### 1. 系统维护
- 定期检查系统健康状态
- 监控性能指标
- 及时处理告警信息

#### 2. 结果分析
- 记录预测结果和实际开奖对比
- 分析预测准确率趋势
- 持续优化预测策略

#### 3. 风险控制
- 不要过度依赖预测结果
- 理性对待预测准确率
- 建立合理的期望值

### 注意事项

1. **数据隐私**: 系统仅用于学习和研究目的
2. **预测限制**: 彩票具有随机性，预测仅供参考
3. **系统稳定性**: 定期更新和维护系统
4. **资源使用**: 合理配置系统资源
5. **法律合规**: 遵守相关法律法规

## 技术支持

如果遇到技术问题，可以：
1. 查看系统日志文件
2. 检查API健康状态
3. 使用Bug检测系统诊断
4. 参考系统架构文档
5. 联系技术支持团队

---

**版本**: v1.0.0
**更新日期**: 2025-01-25
**适用系统**: 福彩3D预测系统

## 附录

### A. 快捷键参考
- `Ctrl + R`: 刷新页面
- `F5`: 重新加载数据
- `Ctrl + S`: 保存当前配置

### B. API接口参考
- 健康检查: `GET /api/v1/health/`
- 数据统计: `GET /api/v1/data/basic-stats`
- 预测状态: `GET /api/v1/prediction/status`

### C. 配置文件说明
- 数据库配置: `src/core/database_manager.py`
- API配置: `src/api/production_main.py`
- UI配置: `src/ui/main.py`
